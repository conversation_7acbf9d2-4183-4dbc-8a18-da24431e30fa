#!/usr/bin/env python3
"""
ONE-TIME ONLY: Convert All Holdings to BTC
This script runs ONCE to convert all current holdings to BTC via Bybit API
After execution, this script should never be run again.
"""

import asyncio
import logging
import os
import sys
from datetime import datetime
from typing import Dict, Any

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - [%(levelname)s] - %(name)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(f'btc_conversion_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)
logger = logging.getLogger(__name__)

class OneTimeBTCConverter:
    """One-time converter to move all holdings to BTC"""
    
    def __init__(self):
        self.bybit_client = None
        self.conversion_complete = False
        
    async def initialize_bybit_client(self):
        """Initialize Bybit client with real credentials"""
        try:
            # Import Bybit client
            sys.path.append('src/exchanges')
            from bybit_client_fixed import BybitClientFixed as BybitClient
            
            # Get credentials from environment
            api_key = os.getenv('BYBIT_API_KEY')
            api_secret = os.getenv('BYBIT_API_SECRET')
            
            if not api_key or not api_secret:
                logger.error("❌ [INIT] Bybit credentials not found in environment variables")
                return False
                
            # Initialize client
            self.bybit_client = BybitClient(api_key, api_secret, testnet=False)
            logger.info("✅ [INIT] Bybit client initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ [INIT] Failed to initialize Bybit client: {e}")
            return False
    
    async def get_all_balances(self) -> Dict[str, float]:
        """Get all current balances from Bybit"""
        try:
            if not self.bybit_client:
                logger.error("❌ [BALANCE] Bybit client not initialized")
                return {}
                
            logger.info("🔍 [BALANCE] Fetching all current balances...")
            balances = await self.bybit_client.get_all_balances()
            
            if not balances:
                logger.warning("⚠️ [BALANCE] No balances found")
                return {}
            
            # Filter out zero balances and format
            significant_balances = {}
            for currency, balance_info in balances.items():
                available = float(balance_info.get('available', 0))
                if available > 0.001:  # Only significant balances
                    significant_balances[currency] = available
                    logger.info(f"💰 [BALANCE] {currency}: {available}")
            
            return significant_balances
            
        except Exception as e:
            logger.error(f"❌ [BALANCE] Error fetching balances: {e}")
            return {}
    
    async def convert_currency_to_btc(self, currency: str, amount: float) -> Dict[str, Any]:
        """Convert a specific currency to BTC"""
        try:
            if currency == 'BTC':
                logger.info(f"⏭️ [CONVERT] Skipping BTC conversion (already BTC)")
                return {'success': True, 'skipped': True, 'reason': 'already_btc'}
            
            logger.info(f"🔄 [CONVERT] Converting {amount} {currency} to BTC...")
            
            # Try direct conversion first (e.g., ETHBTC)
            direct_symbol = f"{currency}BTC"
            try:
                # Check if direct pair exists
                price_info = await self.bybit_client.get_price(direct_symbol)
                if price_info and price_info > 0:
                    logger.info(f"📈 [CONVERT] Direct pair {direct_symbol} available at {price_info}")
                    
                    # Execute direct conversion
                    order_result = await self.bybit_client.create_market_sell_order(
                        symbol=direct_symbol,
                        amount=amount
                    )
                    
                    if order_result and order_result.get('success'):
                        btc_acquired = order_result.get('filled_amount', 0)
                        logger.info(f"✅ [CONVERT] Direct conversion: {amount} {currency} → {btc_acquired:.8f} BTC")
                        return {
                            'success': True,
                            'method': 'direct',
                            'currency': currency,
                            'amount_converted': amount,
                            'btc_acquired': btc_acquired,
                            'order_id': order_result.get('order_id')
                        }
                    else:
                        logger.warning(f"⚠️ [CONVERT] Direct conversion failed: {order_result}")
                        
            except Exception as e:
                logger.debug(f"Direct conversion not available for {currency}: {e}")
            
            # Try via USDT if direct conversion failed
            logger.info(f"🔄 [CONVERT] Trying {currency} → USDT → BTC conversion...")
            return await self.convert_via_usdt(currency, amount)
            
        except Exception as e:
            logger.error(f"❌ [CONVERT] Error converting {currency}: {e}")
            return {'success': False, 'error': str(e)}
    
    async def convert_via_usdt(self, currency: str, amount: float) -> Dict[str, Any]:
        """Convert currency to BTC via USDT intermediate"""
        try:
            # Step 1: Convert to USDT
            usdt_symbol = f"{currency}USDT"
            logger.info(f"🔄 [CONVERT] Step 1: {currency} → USDT")
            
            usdt_order = await self.bybit_client.create_market_sell_order(
                symbol=usdt_symbol,
                amount=amount
            )
            
            if not usdt_order or not usdt_order.get('success'):
                logger.error(f"❌ [CONVERT] Failed to convert {currency} to USDT")
                return {'success': False, 'error': 'USDT conversion failed'}
            
            usdt_acquired = usdt_order.get('filled_amount', 0) * usdt_order.get('avg_price', 0)
            logger.info(f"✅ [CONVERT] Step 1 complete: {amount} {currency} → {usdt_acquired:.2f} USDT")
            
            # Step 2: Convert USDT to BTC
            logger.info(f"🔄 [CONVERT] Step 2: USDT → BTC")
            
            btc_order = await self.bybit_client.create_market_buy_order(
                symbol='BTCUSDT',
                amount=usdt_acquired
            )
            
            if not btc_order or not btc_order.get('success'):
                logger.error(f"❌ [CONVERT] Failed to convert USDT to BTC")
                return {'success': False, 'error': 'BTC conversion failed'}
            
            btc_acquired = btc_order.get('filled_amount', 0)
            logger.info(f"✅ [CONVERT] Step 2 complete: {usdt_acquired:.2f} USDT → {btc_acquired:.8f} BTC")
            
            return {
                'success': True,
                'method': 'via_usdt',
                'currency': currency,
                'amount_converted': amount,
                'btc_acquired': btc_acquired,
                'intermediate_usdt': usdt_acquired,
                'usdt_order_id': usdt_order.get('order_id'),
                'btc_order_id': btc_order.get('order_id')
            }
            
        except Exception as e:
            logger.error(f"❌ [CONVERT] Error in USDT conversion: {e}")
            return {'success': False, 'error': str(e)}
    
    async def execute_one_time_conversion(self):
        """Execute the one-time conversion to BTC"""
        try:
            logger.info("🚀 [START] ONE-TIME BTC CONVERSION STARTING...")
            logger.info("⚠️ [WARNING] This script will run ONCE and never again!")
            
            # Initialize client
            if not await self.initialize_bybit_client():
                logger.error("❌ [FAILED] Cannot proceed without Bybit client")
                return False
            
            # Get all balances
            balances = await self.get_all_balances()
            if not balances:
                logger.warning("⚠️ [COMPLETE] No balances to convert")
                return True
            
            logger.info(f"📊 [SUMMARY] Found {len(balances)} currencies to process")
            
            # Convert each currency
            conversion_results = []
            total_btc_acquired = 0.0
            
            for currency, amount in balances.items():
                if currency == 'BTC':
                    logger.info(f"💎 [BTC] Already have {amount:.8f} BTC")
                    continue
                
                result = await self.convert_currency_to_btc(currency, amount)
                conversion_results.append(result)
                
                if result.get('success') and not result.get('skipped'):
                    btc_acquired = result.get('btc_acquired', 0)
                    total_btc_acquired += btc_acquired
                    logger.info(f"✅ [SUCCESS] {currency}: +{btc_acquired:.8f} BTC")
                else:
                    logger.error(f"❌ [FAILED] {currency}: {result.get('error', 'Unknown error')}")
            
            # Final summary
            logger.info("=" * 60)
            logger.info("🎯 [COMPLETE] ONE-TIME BTC CONVERSION FINISHED!")
            logger.info(f"💰 [TOTAL] BTC acquired from conversions: {total_btc_acquired:.8f}")
            logger.info(f"📈 [PROCESSED] {len(conversion_results)} currencies")
            logger.info(f"✅ [SUCCESS] {len([r for r in conversion_results if r.get('success')])} successful")
            logger.info(f"❌ [FAILED] {len([r for r in conversion_results if not r.get('success')])} failed")
            logger.info("=" * 60)
            
            # Mark as complete
            self.conversion_complete = True
            
            # Create completion marker file
            with open('btc_conversion_completed.marker', 'w') as f:
                f.write(f"BTC conversion completed at {datetime.now()}\n")
                f.write(f"Total BTC acquired: {total_btc_acquired:.8f}\n")
                f.write("This file prevents re-running the conversion script.\n")
            
            logger.info("🔒 [MARKER] Conversion completion marker created")
            return True
            
        except Exception as e:
            logger.error(f"❌ [CRITICAL] One-time conversion failed: {e}")
            return False

async def main():
    """Main execution function"""
    # Check if already completed
    if os.path.exists('btc_conversion_completed.marker'):
        print("🔒 [BLOCKED] BTC conversion already completed!")
        print("📄 [INFO] Found completion marker file")
        print("⚠️ [WARNING] This script should only run ONCE")
        return
    
    print("🚀 [STARTING] One-time BTC conversion...")
    print("⚠️ [WARNING] This will convert ALL holdings to BTC!")
    print("🔄 [INFO] Starting in 5 seconds...")
    
    # 5 second countdown
    for i in range(5, 0, -1):
        print(f"⏰ [COUNTDOWN] {i}...")
        await asyncio.sleep(1)
    
    print("🎯 [EXECUTE] Starting conversion NOW!")
    
    # Execute conversion
    converter = OneTimeBTCConverter()
    success = await converter.execute_one_time_conversion()
    
    if success:
        print("✅ [SUCCESS] One-time BTC conversion completed!")
        print("🎯 [RESULT] All holdings have been converted to BTC")
        print("🔒 [FINAL] This script will not run again")
    else:
        print("❌ [FAILED] One-time BTC conversion failed!")
        print("🔍 [CHECK] Review the logs for details")

if __name__ == "__main__":
    asyncio.run(main())
