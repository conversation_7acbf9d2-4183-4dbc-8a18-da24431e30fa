{"version": "2.0", "projectName": "AutoGPT Trader", "projectType": "ai-cryptocurrency-trading-platform", "systemContext": "Live cryptocurrency trading system operating on REAL MONEY with ZERO TOLERANCE FOR FAILURE. Enterprise-level automated trading platform leveraging cutting-edge neural networks, machine learning, and AI-driven decision making. GOLDEN RULE: MA<PERSON>IMUM PROFIT IN MINIMUM TIME enforcement.", "configuration": {"instructionsFile": ".github/copilot-instructions.md", "augmentFile": ".augment", "systemPrompt": "This is a LIVE CRYPTOCURRENCY TRADING SYSTEM operating on REAL MONEY with ZERO TOLERANCE FOR FAILURE. Follow all comprehensive instructions in .github/copilot-instructions.md. Enforce GOLDEN RULE: MA<PERSON>IMUM PROFIT IN MINIMUM TIME.", "constraints": ["X:\\ drive operation MANDATORY - E:\\ drive FORBIDDEN", "Real market data only - NO simulation", "100% success rate required", "Enterprise production standards", "Maximum neural network coverage", "GOLDEN RULE profit maximization enforcement"]}, "goldenRule": {"principle": "MAXIMUM PROFIT IN MINIMUM TIME", "profitTargets": {"tradeMinimum": "2%", "dailyTarget": "20%", "weeklyTarget": "75%", "monthlyTarget": "300%"}, "capitalAllocation": "90% of available balance per trade", "aiConfidenceThreshold": "65% minimum"}, "neuralSystems": {"metaController": "src/neural/meta_strategy_controller.py", "hftAgent": "src/neural/hft_agent.py", "lstmProcessor": "src/neural/lstm_trading_processor.py", "quantumModules": "src/quantum_trading/", "reinforcementLearning": "src/neural/rl_agents/", "totalSystems": "15+"}, "exchangeAPIs": {"coinbase": {"implementation": "src/exchanges/coinbase.py", "authType": "CDP API with JWT tokens", "features": ["spot", "advanced_trading", "portfolio"]}, "bybit": {"implementation": "src/exchanges/bybit.py", "authType": "Unified Trading Account", "features": ["spot", "futures", "margin", "options"]}}, "security": {"encryptionKeyId": "66c4c378-f65b-4a7d-a23f-37d8936dc66e", "encryptionSystem": "HybridCrypto with post-quantum support", "vaultIntegration": "HashiCorp Vault", "hsmIntegration": "AWS CloudHSM", "postQuantumCrypto": ["Kyber768", "Classic McEliece", "SPHINCS+"]}, "performanceTargets": {"orderLatency": "<100ms", "dataProcessing": "<10ms", "neuralInference": "<50ms", "riskChecks": "<5ms", "systemUptime": "99.9%"}, "documentationMaintenance": {"updateRequirements": ["Update .github/copilot-instructions.md for new features", "Update .augment configuration file", "Update .augmentrc.json with feature descriptions"], "mandatoryProtocol": "All new features MUST be documented"}}